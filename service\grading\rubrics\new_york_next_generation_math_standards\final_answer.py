from service.context_pipeline import logger
from service.context_pipeline import langfuse
from service.utils.supporting_functions import reading_level_example
from concurrent.futures import ThreadPoolExecutor, as_completed
from service.openai_utils.gpt_call import make_openai_langfuse

def final_answer_agent(assignment,student_profile,assignment_type,old_assignment,tools=[],review=False):
    """
    Generates a structured prompt for an LLM-based grading or review agent.

    The agent:
      - Must use a strict 0/100 grading system based ONLY on comparing 'students_answer' with the final answer in 'answerKeys'.
      - If 'answerKeys' contains steps, they must be excluded from grading, except to compute the implied final answer.
      - Uses an image strictly for enhancing feedback (not for changing the grade).
      - Must provide scaffolded, personalized, first-person feedback without revealing the correct solution.

    If 'assignment_type' contains 'review', the agent is an Assignment Reviewer:
      - Evaluates a previously graded assignment (grade + feedback).
      - Ensures the grade is 0 or 100, and the feedback follows all guidelines.

    Otherwise, the agent is an Assignment Grader:
      - Directly assigns 0 or 100 based on correctness.
      - Gives first-person, scaffolded feedback if the answer is incorrect.

    If 'dyslexia' is True, do not penalize minor reversal or calculation errors and focus on conceptual understanding.
    """
    logger.info(f"Starting final_answer_agent with assignment: {assignment}")
    logger.info(f"Student profile: {student_profile}")
    logger.info(f"Assignment type: {assignment_type}")
    logger.info(f"Dyslexia flag: {student_profile.is_dyslexic}")
    logger.info(f"Old assignment provided: {old_assignment is not None}")

    try:
        if hasattr(assignment, 'challenges'):
            challenges=assignment.challenges
        else:
            logger.error("Assignment does not have 'challenges' attribute or is an essay. Treating as a single-item assignment.")
        logger.info(f"Number of challenges to process: {len(challenges)}")

        with ThreadPoolExecutor() as executor:
            futures = {}
            for index, question in enumerate(challenges):
                logger.info(f"Processing challenge {index + 1}/{len(challenges)}")
                logger.info(f"Parsing assignment: {question}")

                previous_assignment_instructions = ""
                if old_assignment:
                    old_challenges=old_assignment['challenges']
                    logger.info(f"Old assignment found: {old_challenges}")
                    old_question = next((item for item in old_challenges if str(item.get('question_number')) == str(question.question_number)), {})
                    logger.info(f"Old question data for question {index}: {old_question}")
                    grade_percentage=old_question.get('grade_percentage', "")
                    students_answer=old_question.get('students_answer', "")
                    personalized_feedback=old_question.get('personalized_feedback', "")
                    rubrics_responses=old_question.get('rubrics_responses', "")
                    messages = langfuse.get_prompt("previous_grading_response_template", label="latest",)
                    logger.info(f"Retrieved prompt for previous grading response: {messages}")
                    previous_assignment_instructions = messages.compile(
                        grade_percentage=grade_percentage,
                        students_answer=students_answer,
                        personalized_feedback=personalized_feedback,
                        rubrics_responses=rubrics_responses
                    )
                    logger.info(f"Previous grading response prompt compiled: {previous_assignment_instructions}")
                dyslexia_instructions = ''
                if student_profile.is_dyslexic:
                    dyslexia_instructions += '''
                        \n\nIMPORTANT: The student has dyslexia. 
                        Do NOT penalize reversals, minor misalignment, or small calculation slips due to processing challenges. 
                        Focus on the logical flow, use of formulas, and clarity of the methodology.'''
                reading_example=reading_level_example(student_profile.reading_level)
                logger.info(f"Reading level example for question {index}: {reading_example}")

                logger.debug("Creating system and user messages for grading via rubrics")
                formatted_question={
                    "question": question.task,
                    "students_answer": question.students_answer,
                    "answerKey": question.answerKey
                }
                if hasattr(question, 'scenario'):
                    formatted_question['scenario']=question.scenario
                if review:
                    messages=langfuse.get_prompt("math_final_answer_review_agent", label="latest")
                    complete_chat_prompt=messages.compile(assignment=formatted_question,student=student_profile,dyslexia_instructions=dyslexia_instructions,reading_level_example=reading_example,old_assignment_instructions=previous_assignment_instructions)
                else:
                    messages=langfuse.get_prompt("math_final_answer_agent", label="latest")
                    complete_chat_prompt=messages.compile(assignment=formatted_question,student=student_profile,dyslexia_instructions=dyslexia_instructions,reading_level_example=reading_example,old_assignment_instructions=previous_assignment_instructions)
                temperature = messages.config.get("openai", {}).get("temperature",0.3)
                model = messages.config.get("openai", {}).get("model","gpt-4o")
                max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
                logger.info(f"Compiled chat prompt for question {index}: {complete_chat_prompt}")
                updated_messages = complete_chat_prompt
                # DISABLE IMAGE FOR FINAL ANSWER GRADE
                # if hasattr(question, 'image_base64') and question.image_base64:
                #     try:
                #         temp_updated_messages = []
                #         for msg in complete_chat_prompt:
                #             if msg.get("role") == "user":
                #                 new_content = [
                #                     {"type": "text", "text": msg["content"]},
                #                     {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{question.image_base64}", "detail": "high"}}
                #                 ]
                #                 temp_updated_messages.append({"role": "user", "content": new_content})
                #             else:
                #                 temp_updated_messages.append(msg)
                #         updated_messages = temp_updated_messages # Assign if successful
                #         logger.info(f"Prepared message with image for question (ID/Num: {question.question_number}).")
                #     except Exception as e_img:
                #         logger.error(f"Error preparing image content for question (ID/Num: {question.question_number}): {e_img}", exc_info=True)
                # Fallback to complete_chat_prompt if image processing fails
                
                
                logger.info(f"Generated tools for question {index}: {tools}")

                future  = executor.submit(make_openai_langfuse,updated_messages, tools, model, temperature, max_tokens)
                logger.info(f"Created future object for question {index}: {future}")
                futures[future] = index
                logger.info(f"Submitted grading request for question {index}, future object: {future}")

            logger.info("Processing completed futures from grading requests")
            for future in as_completed(futures):
                index = futures[future]
                logger.info(f"Processing completed future for question {index}")
                try:
                    response = future.result()
                    logger.info(f"Response for question {index}: {response}")
                    logger.info(f"response:{response}")
                    response=response['graded_response']
                    grade = response['grade']
                    logger.info(f"grade:{grade}")

                    grade = round(float(grade), 2)
                    logger.info(f"Rounded grade for question {index}: {grade}")
                    logger.info(f"Updating challenges: {challenges}")
                    challenges[index].final_answer_grade=grade
                    if hasattr(assignment, 'personalized_feedback'):
                        challenges[index].personalized_feedback += '\n\n' + response['personalized_feedback']
                        logger.info(f"Updated personalized feedback for question {index}: {challenges[index].personalized_feedback}")
                    else:
                        challenges[index].personalized_feedback = response['personalized_feedback']

                    #challenges[index].grade_percentage=grade
                except Exception as e:
                    logger.error(f"Error processing non-essay grading on question {index}: {e}")
        if hasattr(assignment, 'challenges') and 'essay' not in assignment_type:
            assignment.challenges=challenges
        logger.info(f"Final assignment with updated challenges: {assignment.dict()}")
        logger.info("Successfully completed grade_via_rubrics processing")
        return assignment
    except Exception as e:
        logger.error(f"Error in final_answer_agent: {e}")
        raise
    
