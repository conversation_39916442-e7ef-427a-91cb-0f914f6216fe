from service.grade_data_models import GradePipelineContext
from service.grading.common_agents.feedback_questions_agent import answer_feedback_questions
from service.context_pipeline import logger

def answer_feedback_questions_node(state: GradePipelineContext):
    """
    Node that handles assessment of character strengths in assignments.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting answer_feedback_questions_node")

    assignment = state.assignment
    student_profile = state.student
    assignment_type = state.request.assignment_type
    old_assignment = state.previous_response
    context = state.assignment.passage if hasattr(state.assignment, 'passage') else ''


    try:
        updated_assignment = answer_feedback_questions(
            assignment, student_profile, assignment_type, old_assignment, context
        )
        return {"assignment": updated_assignment}

    except Exception as e:
        logger.error(f"Error in answer_feedback_questions_node: {e}")
        # Return no adjustments explicitly in case of error
        return {}
