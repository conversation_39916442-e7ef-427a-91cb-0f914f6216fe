from service.context_pipeline import logger, aisdk_object
from service.grading.graphs.ela.general_ela_graph import general_ela_graph
from service.grading.graphs.math.general_math_graph import math_general_graph
# from service.graphs.ela.essay_langgraph import ela_essay_langgraph
# from service.graphs.maths.general_langgraph import new_math_langgraph
# from service.graphs.history.general_langgraph import history_general_langgraph

def route_assignment(ptx,langfuse_handler):
    """
    Selects and calls the appropriate LangGraph function based on assignment_type.

    Args:
        assignment_type: The type of assignment (must be a key in LANGGRAPH_FUNCTION_MAP).
        **kwargs: Keyword arguments to pass directly to the target LangGraph function
                  (e.g., user_input="...", context=None, topic="WWII").

    Returns:
        The result returned by the executed LangGraph function.

    Raises:
        ValueError: If the provided assignment_type is not found in the map.
        TypeError: If the mapped item isn't a callable function.
        Exception: Re-raises any exception originating from the called LangGraph function.
    """
    #logger.info(f"Router received type: {ptx}")
    assignment_type=ptx.request.assignment_type
    LANGGRAPH_FUNCTION_MAP = {
        "reading_comp_gen": general_ela_graph,
        "vocab_fill": general_ela_graph,
        "arg_essay": general_ela_graph,
        "inf_essay": general_ela_graph,
        "math_worked_example": math_general_graph,
        "math_fact": math_general_graph,
        "math_home_dynamic": math_general_graph,
        "math_partner": math_general_graph,
        "math_word_problem": math_general_graph,
        "math_story": math_general_graph,
        "history_critical": general_ela_graph,
        "history_fact": general_ela_graph,
        #"history_essay": general_ela_graph,
    }

    logger.info(f"Router received type: {assignment_type}")

    # Retrieve the target function object from the map using the assignment_type string
    target_function = LANGGRAPH_FUNCTION_MAP.get(assignment_type)
    try:
        result = target_function(ptx,langfuse_handler)
        logger.info(f"Router finished processing type: {assignment_type}")
        return result 
    except Exception as e:
        logger.error(f"Error during execution of function for {assignment_type}")
        logger.error(f"Function: {getattr(target_function, '__name__', 'N/A')}")
        logger.error(f"Error details: {e}")
        raise
