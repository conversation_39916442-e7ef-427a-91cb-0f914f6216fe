from service.grade_data_models import GradePipelineContext
from service.grading.common_agents.summarize_personalized_feedback_agent import summarize_personalized_feedback
from service.context_pipeline import logger

def summarize_personalized_feedback_node(state: GradePipelineContext):
    """
    Node that handles assessment of character strengths in assignments.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting summarize_personalized_feedback_node")

    assignment = state.assignment
    student_profile = state.student
    assignment_type = state.request.assignment_type
    context = state.assignment.passage if hasattr(state.assignment, 'passage') else ""

    try:
        overall_personalized_feedback = summarize_personalized_feedback(
            student_profile,
            assignment,
            assignment_type,
            context
        )
        
        return {"overall_personalized_feedback": overall_personalized_feedback}

    except Exception as e:
        logger.error(f"Error in summarize_personalized_feedback_node: {e}")
        # Return no adjustments explicitly in case of error
        return {}
