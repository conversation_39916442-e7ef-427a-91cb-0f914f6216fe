import json
import re
import requests 
from service.context_pipeline import logger
from service.grading.common_agents.reading_level_agent import get_guideline_by_level
import ast

def add_feedback_and_calculate_average(assignments, feedback, engagement=False):
    """
    Integrates feedback and grades into assignment questions and calculates the overall average grade.

    Parameters:
    - assignments (list of dict): Contains details of each question, including 'index' (question number), 
      'answerKeys' (correct answer), 'description', 'scenario', 'task' (question), 'title', and 'students_answer'.
    - feedback (list of dict): Contains feedback for each question, including 'index' (question number),
      various grade-related fields, and 'personalized_feedback'.

    Returns:
    - list: Updated list of assignments with all feedback keys integrated.
    - float: The overall average grade for the assignment, calculated from a grade-related field in the feedback.
             We assume that the field name ends with 'grade'.
    """
    print("assignments:", assignments)
    print("feedback:", feedback)
    normalized_feedback = []
    for item in feedback:
        if isinstance(item, dict):
            normalized_item = {key.lower(): value for key, value in item.items()}
            normalized_feedback.append(normalized_item)
        else:
            print(f"Skipping non-dictionary item: {item}")
    print("normalized_feedback:", normalized_feedback)
    
    feedback_dict = {int(item['index']): item for item in normalized_feedback if 'index' in item}
    print("feedback_dict:", feedback_dict)

    total_grade = 0
    count = 0 

    for assignment in assignments:
        index = int(assignment.get('index', -1))  
        
        if index in feedback_dict:
            feedback_item = feedback_dict[index]
            
            for key, value in feedback_item.items():
                if key != 'index':
                    assignment[key] = value
            
            if not engagement:
                for k, v in assignment.items():
                    if k.lower().endswith('grade'):
                        try:
                            total_grade += round(float(v), 2)
                            count += 1
                            break
                        except ValueError:
                            pass
    print("total_grade:", total_grade)
    print("count:", count)
    if count==0:
        average_grade = 0
    else:
        average_grade = total_grade / count if count else 0

    if not engagement:
        return assignments, average_grade
    else:
        return assignments
    
def reading_level_example(readinglevel_sent):
    guidelines = get_guideline_by_level(readinglevel_sent)
    if readinglevel_sent == '1st':
        example = "'Aidan's cat jumps far. How many times can it jump in 4 tries?', 'Aidan sees rain in a cup. How much rain in 2 hours?', 'Aidan claps with the music. How many claps in 10 seconds?', 'Aidan sees apples on trees. How many apples are there?', 'Aidan has books on a shelf. How many books on 3 shelves?'"
    elif readinglevel_sent == '2nd':
        example = "'Aidan has apples in 2 baskets. Count all the apples.', 'Sara has 5 red balloons and 3 blue ones. Count her balloons.', 'Tom reads 4 pages in a book each day. How many pages does he read in 7 days?', 'Jenny had 10 candies. She gives 2 away to her friends. How many does she have now?', '1 box has 6 crayons. How many in 4 boxes?'"
    elif readinglevel_sent == '3rd':
        example = "'Sara counts 5 apples in her basket, then adds 3 more. Can you tell how many apples she has in total?', 'Tom owns 4 toy cars. If he gets 2 more as gifts, can you calculate his new total of toy cars?', 'Jenny bakes 6 cupcakes. If she decides to eat 1, how many cupcakes does she have left?', 'Alex reads 2 books every week. How many books does he read after 4 weeks?', 'Mia has a collection of 10 pencils. If she gives 2 pencils to her friend, how many pencils does she have now?'"
    elif readinglevel_sent == '4th':
        example = "Here is a list exmemplifying vocab words for 4th grade: 'Word', 'range', 'essay', 'condition', 'scientific', 'reference', 'increase', 'growth', 'convert', 'customary', 'generalization', 'acute', 'inference', 'conclude', 'role', 'comparison', 'partial', 'select', 'specific', 'basic', 'progress', 'actual', 'appropriate', 'task', 'ability', 'conduct', 'conflict', 'influence', 'persuasive', 'primary', 'audience', 'historical', 'organization', 'positive', 'precise', 'transform', 'unfamiliar', 'variety', 'vary', 'boundary', 'career', 'exist', 'narrow', 'preserve', 'publish', 'reduce', 'simplify', 'suggest', 'advertisement', 'descriptive', 'eventually', 'involve', 'prevent', 'endanger', 'error', 'examine', 'generate', 'require', 'decrease', 'differ', 'draft', 'evenly', 'inspire', 'limit', 'rate', 'beyond', 'discussion', 'forecast', 'gain', 'individual', 'original', 'prove', 'accurate', 'composite', 'convince', 'establish', 'inform', 'instruction', 'particular', 'relative', 'safely', 'argument', 'complex', 'definite', 'department', 'outline', 'prompt', 'diverse', 'extreme', 'persuade', 'quantity', 'realistic', 'benefit', 'clarify', 'comprehend', 'historic', 'production', 'similarity', 'decimal', 'ounce', 'height', 'equivalent', 'per', 'quotient', 'cm', 'range'. Now for some sentence examples.'Sam found 8 apples in one basket and another 7 in a different one. How many apples are there in all?', 'Lily possesses 10 stickers but gives 3 to her friend. How many stickers remain with Lily?', 'In the lot, there are 5 cars, and each one is equipped with 4 wheels. Can you calculate the total number of wheels present?', 'Every night, Jake dedicates time to read 2 pages of his book. How many pages will he have read after 7 nights?', 'Emma commits to saving 2 dollars each week. What will be her total savings after a period of 4 weeks?'"
    elif readinglevel_sent == '5th':
        example = "'Aidan looks at the clock and watches 5 seconds go by. How far can each player run?', 'In math, Aidan learns about multiplication. He uses it to work out how far the players run.', 'Player 1 is quick, moving 7 yards in a second. How far does he travel in 5 seconds?', 'Player 5 speeds along at 35 yards in one second. Aidan works out his distance after 5 seconds.', 'Aidan keeps a chart to track how fast and far each player goes.', 'Aidan knows that to win, it\'s important to understand how speed and distance work together.', 'Aidan gets better at this by doing his math homework, calculating distances for his team.'"
    elif readinglevel_sent == '6th':
        example = "'During his art project, Leo carefully measures to draw lines exactly 6 inches long. He wonders about the number of 2-inch sections he can divide these lines into.', 'The library is 5 blocks from Tom\'s house. He\'s trying to figure out the total number of blocks he would walk if he goes there and back over the course of 4 days.', 'Mia\'s recipe requires 3/4 cup of sugar. She\'s doubling the recipe and needs to know the total amount of sugar necessary.'"
    elif readinglevel_sent == '7th':
        example = "'In science, Sarah tracks the growth of her plant, measuring a 2-inch increase in the first week.', 'In art, Lucas uses a grid to scale his drawing, with each square is 5 square inches of the final piece.', 'For geography, Emma figures out the average rainfall in her city over four months. She notices that there were 10 inches in the first month.', 'At the fundraiser, Jake sells candy bars, wondering how many he could sell in three hours after selling 15 in the first hour.', 'In music, Mia times her piano practice, starting with 20 minutes on Monday and planning to double it by Friday.'"
    elif readinglevel_sent == '8th':
        example = "'Sarah has to figure out what x equals in the math problem 3x + 5 = 20 to know x\'s value.', 'In science, Mike finds out how dense something is by using its weight and how much space it takes up.', 'Julia is making a chart to show how a plant gets taller over time, using centimeters to measure.', 'Emma is working out how much money is needed for a school event by adding the costs of all the things needed.', 'For his project on places around the world, Leo is changing kilometers into miles to see how far apart two cities are.'"
    elif readinglevel_sent == '9th':
        example = "'Aidan looked at the science experiment\'s numbers to see how fast reactions happened, timing them in seconds.', 'During math, he figured out the amount of water moving through a pipe in five minutes by solving problems.', 'He observed how fast plants grew, tracking their height increase in inches every week.', 'For his history assignment, Aidan mapped out how territories got bigger over years, turning years into seconds to see it differently.', 'In art class, he worked out how long it takes for paint layers to dry, using what he knows about speeds.'"
    elif readinglevel_sent == '10th':
        example = "'The physics teacher gave out a project on how things move when thrown, asking for calculations on how fast they start moving and how far they go.', 'In the chemistry lab, students have to figure out how quickly reactions happen, measured in moles per second, for their experiments.', 'For the math contest, Julia has to work through tough algebra problems that include quadratic functions.', 'In the calculus class, the homework is about finding derivatives of functions to get how things speed up or slow down.', 'The engineering club is working on a bridge design, and they need to figure out how much force the cables can hold with different weights.'"
    elif readinglevel_sent == '11th':
        example = "'The coach presented a detailed diagram to illustrate each runner\'s pace in meters per minute.', 'Students were tasked to compute the total distance each runner covered in a 10-minute sprint.', 'In the geometry class, the assignment involved calculating the angle of elevation for a soccer ball kicked at an initial speed of 20 meters per second.', 'For physics homework, students calculated the acceleration of a car that accelerates from 0 to 60 miles per hour in 7 seconds.', 'Maya planned to explore the connection between the angle of a ramp and the speed of a ball rolling down for her science project.'"
    elif readinglevel_sent == '12th':
        example = "'The physics teacher presented a complicated formula to figure out how fast something speeds up, which required advanced problem-solving abilities.', 'In higher-level calculus, learners delve into the idea of derivatives to grasp how quickly things change under different conditions.', 'The stats course involves dissecting data set variations, an essential ability for decoding research findings.', 'Grasping trigonometry\'s core ideas is key to tackling issues related to angles and distances in more complex math.', 'Examining geometric proofs lays the groundwork for logical thought and critical analysis in more advanced mathematical studies.', 'Using integrals in real-life scenarios pushes students to adapt theoretical ideas to practical issues.'"
    elif readinglevel_sent == 'kindergarten':
        example = "'Aidan is happy. How many apples does he have?', 'Sara sees a red ball. What shape is it?', 'Tom has 3 pencils. How many more does he need to make 5?', 'Jenny likes the colour yellow'"

    return f"Reading level: {guidelines}. Here are some examples: {example}"

def calculate_grades(questions, max_grade):
    max_grade_per_criteria = max_grade
    print("max_grade:", max_grade_per_criteria)

    # Ensure `questions` is a list of dictionaries, even if a single dictionary is provided
    if isinstance(questions, dict):
        questions = [questions]

    for question in questions:
        # Extract criteria dynamically from the question keys
        criteria = [key for key in question.keys() if key.endswith('_grade')]

        # Calculate the total score based on the extracted criteria
        total_score = sum(int(question[criterion]) for criterion in criteria)
        max_possible_score = len(criteria) * max_grade_per_criteria
        if len(criteria) == 0:
            question['grade'] = total_score
            question['grade_percentage'] = int((total_score / max_grade_per_criteria) * 100) 
        else:
            # round to the nearest 2 decimal places
            question['grade'] = round(total_score / len(criteria), 2)
            question['grade_percentage'] = int((total_score / max_possible_score) * 100) 

    # Calculate the overall grade for all questions
    total_grade = sum(question['grade'] for question in questions) / len(questions)
    return questions, total_grade

def organize_feedback(feedback_dict):
    feedback_parts = []

    # Add opening feedback if exists
    if 'praise_feedback' in feedback_dict:
        feedback_parts.append(feedback_dict['praise_feedback'] + '\n\n')

    # Iterate through all possible critical feedback dynamically
    idx = 0
    while True:
        key = f'critical_feedback_{idx}' if (idx := idx+1 if 'idx' in locals() else 1) else None
        if key := feedback_dict.get(key):
            feedback_parts.append(key+ '\n\n')
        else:
            break

    # Adding the closing statement
    if 'closing_statement' in feedback_dict:
        feedback_parts.append(feedback_dict['closing_statement'])

    return ' '.join(feedback_parts)

def update_tools_structure(questions_yes,questions_no):
    print("questions_yes:",questions_yes)
    print("questions_no:",questions_no)
    # Define the base JSON structure
    base_structure = {
        "type": "function",
        "function": {
            "name": "generate_feedback",
            "description": "Generate a constructive feedback for the student based on his performnance",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    }
    base_structure["function"]["parameters"]["properties"]['index']={
        "type": "string",
        "description":"number of the question"
        }
    base_structure["function"]["parameters"]['required'].append('index')
    if questions_yes['rubrics_responses']:
        base_structure["function"]["parameters"]["properties"]['praise_feedback'] = {
            "type": "string",
            "description":"praise and compliment the student on his correct answer using the questions in rubrics_responses"
            }
        base_structure["function"]["parameters"]['required'].append('praise_feedback')
        closing_desc = "Include a positive closing statement acknowledging the student's perfect performance."
    
    if questions_no['rubrics_responses']:
        idx=1
        for question_id, question_text in questions_no.get('rubrics_responses', {}).items():
            feedback_key = f"critical_feedback_{idx}"
            base_structure["function"]["parameters"]["properties"][feedback_key] = {
                'type': 'string',
                'description': f"Constructive feedback addressing: {question_text['question_text']}"
            }
            if feedback_key not in base_structure["function"]["parameters"]['required']:
                base_structure["function"]["parameters"]['required'].append(feedback_key)
            idx += 1
        closing_desc = (
                "Include a statement that encourages resubmission and guides improvement without giving away answers."
                "The statement should be clear and actionable."
            )
    base_structure['function']['parameters']['properties']['closing_statement']={
        "type":'string',
        "description":closing_desc
    }
    base_structure["function"]["parameters"]['required'].append('closing_statement')

    print("tools structure",base_structure)
    return [base_structure]


def merge_multiple_responses(*responses, agent_types, state_standard=''):
    def get_personalized_feedback(response_items):
        feedbacks = [response.get('personalized_feedback', '') for response in response_items if 'personalized_feedback' in response]
        return "\n\n - ".join(feedbacks).lstrip('\n\n - ')

    def get_grades(response_items):
        total_grade = 0
        grades = {}
        count = 0

        for response in response_items:
            # Dynamically find all keys ending with '_grade'
            grade_keys = [key for key in response.keys() if key.endswith('_grade')]
            for key in grade_keys:
                # Keep the specific agent's grade (if multiple responses have the same key)
                grades[key] = response[key]
                total_grade += round(float(response[key]), 2)
                count += 1

        if count > 0:
            if state_standard == 'peekskill city school district':
                grades['grade_special'] = str(total_grade // count) 
            else:
                grades['grade'] = str(total_grade // count)  # Overall average grade
            if state_standard == 'peekskill city school district':
                grades['grade_percentage_special']= (int(float(grades['grade_special']))/2)*100
            else:
                grades['grade_percentage']= (int(float(grades['grade']))/5)*100

        return grades

    merged_response = []
    engagement_grade_sum = 0
    num_items = len(responses[0])

    for i in range(num_items):
        response_items = [response[i] if isinstance(response, list) else response for response in responses]
        merged_item = response_items[0].copy()

        # Merge personalized feedback
        merged_item['personalized_feedback'] = get_personalized_feedback(response_items)

        if 'engagement' in agent_types:
            # Aggregate engagement grades from all given responses if they exist
            engagement_values = []
            for r in response_items:
                if 'engagement_grade' in r:
                    engagement_values.append(round(float(r['engagement_grade']), 2))

            if engagement_values:
                avg_engagement = sum(engagement_values) / len(engagement_values)
                merged_item['engagement_grade'] = str(round(avg_engagement, 2))
                engagement_grade_sum += avg_engagement
            else:
                # If no engagement grades were found, fallback to regular grade calculation
                merged_item.update(get_grades(response_items))
        else:
            # If not using engagement as a type, just get the regular grades
            merged_item.update(get_grades(response_items))

        merged_response.append(merged_item)

    if 'engagement' in agent_types:
        total_engagement_grade = engagement_grade_sum // num_items
        return merged_response, total_engagement_grade
    else:
        return merged_response

def remove_duplicates_by_attempt(data):
    """
    Remove duplicate entries in a JSON structure based on the `task` key,
    keeping the entry with the highest `attempt` value.

    Args:
        data (dict): JSON data containing challenges.

    Returns:
        dict: JSON data with duplicates removed.
    """

    if 'challenges' in data:
        challenges = data["challenges"]
    else:
        challenges = data
    unique_challenges = {}
    index_data={}
    for challenge in challenges:
        task = challenge.get("task")
        if not task:
            continue
        
        # Parse the attempt to ensure numeric comparison
        try:
            attempt = int(challenge.get("attempt", 0))
        except ValueError:
            attempt = 0  # Default to 0 if the attempt cannot be converted to int

        # Check if the task already exists and replace if the current attempt is higher
        if task in unique_challenges:
            existing_attempt = unique_challenges[task]["attempt"]
            try:
                existing_attempt = int(existing_attempt)
            except ValueError:
                existing_attempt = 0

            if attempt > existing_attempt:
                unique_challenges[task] = challenge
        else:
            unique_challenges[task] = challenge
        
        if 'question_number' in challenge:
            index_data[challenge['question_number']]=challenge['index']
            challenge['index']=challenge["question_number"]
        elif 'questionNumber' in challenge:
            index_data[challenge['questionNumber']]=challenge['index']
            challenge['index']=challenge["questionNumber"]
    #data["challenges"] = list(unique_challenges.values())
    return index_data,list(unique_challenges.values())

def separate_latest_attempts(data):
    """
    Separates JSON entries based on the latest and second-latest attempts.
    
    Args:
        data (dict): JSON data containing challenges.

    Returns:
        tuple: Two lists containing `latest_attempts` and `previous_attempts` (or None if no second attempts exist).
    """
    if 'challenges' in data:
        challenges = data["challenges"]
    else:
        challenges = data
    
    latest_attempts = {}
    previous_attempts = {}
    
    for challenge in challenges:
        task = challenge.get("task")
        if not task:
            continue
        question_number = challenge.get("questionNumber")
        if question_number is not None and challenge.get("index") != question_number:
            challenge["index"] = question_number
        # Parse the attempt to ensure numeric comparison
        try:
            attempt = int(challenge.get("attempt", 0))
        except ValueError:
            attempt = 0  # Default to 0 if the attempt cannot be converted to int

        if task in latest_attempts:
            existing_attempt = int(latest_attempts[task]["attempt"])
            
            if attempt > existing_attempt:
                # Move the previous latest attempt to previous_attempts
                previous_attempts[task] = latest_attempts[task]
                latest_attempts[task] = challenge
            elif attempt > int(previous_attempts.get(task, {}).get("attempt", -1)):
                previous_attempts[task] = challenge
        else:
            latest_attempts[task] = challenge
            previous_attempts[task] = {}  # Initialize as empty in case no second attempt exists
    
    # Convert dictionaries to lists
    latest_attempts_list = list(latest_attempts.values())
    previous_attempts_list = list(filter(None, previous_attempts.values()))  # Remove empty values

    # Return None if no second attempts exist
    return latest_attempts_list, previous_attempts_list if previous_attempts_list else None


def split_rubric_responses(data):
    if type(data)==str:
        data = json.loads(json.dumps(data))  
    #print('data type in split rubrics responses:',type(data))
    if 'index' in data:
        data.pop('index')
    #print('data:',data)
    # Extract rubric responses
    rubric_responses = data.get("rubrics_responses", {})
    if 'index' in rubric_responses:
        rubric_responses.pop('index')
    # Create two separate dictionaries for True and False responses
    #true_responses = {key: value for key, value in rubric_responses.items() if value.get("answer")}
    #false_responses = {key: value for key, value in rubric_responses.items() if not value.get("answer")}
    true_responses = {key: value for key, value in rubric_responses.items() if value.get("answer").lower() == 'true'}
    false_responses = {key: value for key, value in rubric_responses.items() if value.get("answer").lower() != 'true'}
    
    # Create deep copies of the original dictionary with modified rubric_responses
    true_dict = data.copy()
    true_dict["rubrics_responses"] = true_responses
    
    false_dict = data.copy()
    false_dict["rubrics_responses"] = false_responses
    
    return true_dict, false_dict

    
# def merge_multiple_responses(*responses, agent_types):
#     def get_personalized_feedback(response_items):
#         feedbacks = [response['personalized_feedback'] for response in response_items]
#         return "\n\n - ".join(feedbacks).lstrip('\n\n - ')

#     def get_grades(response_items, agent_types):
#         total_grade = 0
#         grades = {}
#         for j, response in enumerate(response_items):
#             agent_grade_key = f"{agent_types[j]}_grade"
#             grades[agent_grade_key] = response['grade']
#             total_grade += int(response['grade'])
#         grades['grade'] = str(total_grade / len(response_items))
#         return grades

#     merged_response = []
#     engagement_grade = 0
#     num_items = len(responses[0])

#     for i in range(num_items):
#         response_items = [response[i] if isinstance(response, list) else response for response in responses]
#         merged_item = response_items[0].copy()
#         merged_item['personalized_feedback'] = get_personalized_feedback(response_items)

#         if 'engagement' in agent_types:
#             merged_item['engagement_grade'] = response_items[1]['grade']
#             engagement_grade += int(merged_item['engagement_grade'])
#         else:
#             merged_item.update(get_grades(response_items, agent_types))

#         merged_response.append(merged_item)

#     if 'engagement' in agent_types:
#         total_engagement_grade = engagement_grade / num_items
#         return merged_response, total_engagement_grade

#     else:
#         return merged_response

def calculate_overall_grade(assignments):
    """
    Calculate the overall grade for assignments based on weights.

    Parameters:
    assignments (list): List of assignment dictionaries with 'method_grade' and 'final_answer_grade'.

    Returns:
    list: Updated assignments with 'overall_grade' added.
    """
    import logging

    logging.info(f"Starting grade calculation for {len(assignments)} assignments")

    try:
        assignment_grade = 0

        if not assignments:
            logging.warning("Empty assignments list provided")
            return assignments, 0

        for i, assignment in enumerate(assignments):
            try:
                logging.info(f"Processing assignment {i+1}/{len(assignments)}")

                method_grade = round(float(getattr(assignment, "method_grade", 0)), 2)
                final_answer_grade = round(float(getattr(assignment, "final_answer_grade", 0)), 2)

                logging.info(f"Assignment {i+1}: method_grade={method_grade}, final_answer_grade={final_answer_grade}")

                if final_answer_grade == 100:
                    overall_grade = (0.2 * method_grade) + (0.8 * final_answer_grade)
                    logging.info(f"Assignment {i+1}: Using perfect score weighting (20% method, 80% final)")
                elif final_answer_grade == 0:
                    overall_grade = (0.5 * method_grade) + (0.5 * final_answer_grade)
                    logging.info(f"Assignment {i+1}: Using zero score weighting (50% method, 50% final)")
                else:
                    overall_grade = (0.2 * method_grade) + (0.8 * final_answer_grade)
                    logging.info(f"Assignment {i+1}: Using standard weighting (20% method, 80% final)")

                overall_grade = round(overall_grade, 2)
                logging.info(f"Assignment {i+1}: calculated overall_grade={overall_grade}")

                assignment.grade = str(overall_grade)
                assignment.grade_percentage = str(overall_grade)
                assignment_grade += overall_grade

            except (ValueError, AttributeError, TypeError) as e:
                logging.error(f"Error processing assignment {i+1}: {str(e)}")
                logging.error(f"Assignment data: {assignment}")
                # Set default values and continue
                assignment.grade = "0"
                assignment.grade_percentage = "0"
                continue

        assignment_grade = round(assignment_grade / len(assignments), 2)
        logging.info(f"Grade calculation completed. Average grade: {assignment_grade}")

        return assignments, assignment_grade

    except Exception as e:
        logging.error(f"Critical error in calculate_overall_grade: {str(e)}")
        logging.error(f"Assignments data: {assignments}")
        raise


def calculate_grade_from_questions(data, max_value=4):
    print("DATA TO CALCULATE GRADE:", data)
    
    # Filter out only question entries
    questions = {key: value for key, value in data.items() if key.startswith("question")}
    
    # Total number of questions
    total_questions = len(questions)
    
    # Define mark mapping
    def get_marks(answer):
        """Returns marks based on the answer, accounting for different variations."""
        answer = str(answer).strip().lower()
        if "true" in answer:  # Covers 'True', 'true', 'TRUE', etc.
            return 2
        elif "partial" in answer:  # Covers 'Partial', 'Partially', 'partial', 'partially'
            return 1
        elif "false" in answer:  # Covers 'False', 'false', 'FALSE', etc.
            return 0
        return 0  # Default case (unexpected values)

    # Compute total marks based on responses
    total_marks = sum(get_marks(question["answer"]) for question in questions.values())

    # Calculate max possible marks (2 per question)
    max_possible_marks = total_questions * 2 if total_questions > 0 else 1  # Avoid division by zero
    
    # Scale the grade to the max_value
    grade = (total_marks / max_possible_marks) * max_value
    
    return grade

    # Return the results
    # return {
    #     "total_questions": total_questions,
    #     "true_answers": true_answers,
    #     "grade_out_of_max": grade
    # }

def extract_question_text(rubric_list):
    questions=[]
    for item in rubric_list:
        if hasattr(item, 'rubric_question_text'):
            questions.append(item.rubric_question_text)
        else:
            logger.info(f"Failed to extract question text from {item}")
            return rubric_list
    return questions
        # elif 'rubric_question_text' in item:
        #     return item['rubric_question_text']


def is_properly_structured(text: str) -> bool:
    """Checks if the text follows the bullet point structure."""
    points = re.split(r'(?<=[.?])\s+', text.strip())
    print(points)
    return all(point.strip().startswith('-') for point in points) and len(points) > 1

def fix_structure(text: str) -> str:
    """Fixes the structure of bullet points to ensure each starts with '-' and is separated by '\n\n'."""
    sentences = re.split(r'(?<=[.?])\s+', text.strip())
    fixed_points = []
    
    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue
        if not sentence.startswith('-'):
            sentence = '- ' + sentence
        fixed_points.append(sentence)
    
    return '\n\n'.join(fixed_points)
def add_cors_headers(response):
    """Add CORS headers to response"""
    try:
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add("Access-Control-Allow-Headers", "Content-Type,Authorization,X-Custom-Header")
        response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
        return response
    except Exception as e:
        logger.error(f"Error adding CORS headers: {str(e)}")
        return response
    
def send_qa_request(inputs, final_results):
    """Send QA request"""
    try:
        qa_request = {
            "inputs": inputs,
            "output": final_results,
            "endpoint_type": "grade",
            "env": "prod"
        }
        requests.post(
            "https://us-central1-bubbly-granite-412822.cloudfunctions.net/qa_server",
            json=qa_request
        )
    except Exception as e:
        logger.error(f"Error sending QA request: {str(e)}")


def generate_question_json(questions):
    
    """
    Generates a structured JSON schema for answering a list of questions.

    This function dynamically constructs a JSON structure that:
    - Contains a function named "answer_list_of_questions"
    - Allows answering each question using True/False/Partial
    - Provides a reason for each answer
    - Includes a field for personalized feedback

    Parameters:
    - questions (list of str): A list of questions to be included in the JSON schema.

    Returns:
    - list: A list containing the structured JSON schema, formatted for use in LLMs or APIs.
    """
    # Define the base JSON structure
    base_structure = {
        "type": "function",
        "function": {
            "name": "answer_list_of_questions",
            "description": "Answers a list of questions using Yes/No Only",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    }

    # Populate the JSON structure with questions
    for i, question in enumerate(questions, start=1):
        question_key = f"question{i}"
        base_structure["function"]["parameters"]["properties"][question_key] = {
            "type": "object",
            "properties": {
                "question_text": {
                    "type": "string",
                    "description": f"The text of question {i}"
                },
                "answer": {
                    "type": "string",
                    "description": f"Answer for question {i} True/False/Partial"
                },
                "reason": {
                    "type": "string",
                    "description": f"Reason for the chosen answer for the question"
                }
            },
            "required": ["question_text", "answer", "reason"]
        }
        base_structure["function"]["parameters"]["required"].append(question_key)

    # Add the single personalized feedback field
    base_structure["function"]["parameters"]["properties"]["personalized_feedback"] = {
        "type": "string",
        "description": "Personalized feedback for all questions"
    }
    base_structure["function"]["parameters"]["required"].append("personalized_feedback")

    print("tools structure",base_structure)
    return [base_structure]


def simplify_challenges(assignment, question_number):
    
    """
    Simplifies a given assignment by extracting and returning only the relevant question 
    based on the provided question number.

    This function processes the assignment, which may be a string (JSON-like format) or a list of dictionaries.
    If the assignment contains multiple questions, it filters out only the question matching the given question_number.

    Parameters:
    - assignment (str or list): The assignment data, which can be a JSON-like string or a list of dictionaries.
    - question_number (str or int): The specific question index to extract.

    Returns:
    - list: A list containing the extracted question dictionary. If the assignment is already a list, 
      it maintains the same structure.
    """    
    if type (assignment)==str:
        assignment=ast.literal_eval(assignment)
    print("simplify_challenges used")

    for question in assignment:
        if "index" in question and str(question["index"]) == str(question_number):
            assignment=question
    print("ASSIGNMENT:",assignment)
    if type(assignment)==list:
        return assignment
    else:
        return [assignment]

