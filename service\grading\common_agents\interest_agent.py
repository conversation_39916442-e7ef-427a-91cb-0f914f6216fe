from service.openai_utils.gpt_call import make_openai_langfuse,make_openai_langfuse_websearch
from service.context_pipeline import logger, aisdk_object
from service.context_pipeline import langfuse
import random
from concurrent.futures import ThreadPoolExecutor, as_completed



def interest_search_agent(interest,grade,search_queries):    
    messages = langfuse.get_prompt('interest_search_agent', type='chat', label="latest")
    prompt = messages.compile(interest=interest, grade=grade,search_queries=search_queries)
    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)

    response=make_openai_langfuse_websearch(prompt, model, temperature,max_tokens)
    return response

def supporting_interest_agent(graded_assignment: dict, interest_information: str,assignment_type: str, student_profile: dict) -> dict:
    """
    Aligns assignments to student interests by generating fun facts and current information
    using a single OpenAI API call with web search capability.

    Returns:
        dict: {
            "adjustment_instructions": str (empty if already included)
        }
    """
    logger.info(f"Starting supporting_interest_agent with graded_assignment: {graded_assignment}")
    try:
        if hasattr(graded_assignment, 'challenges') and 'essay' not in assignment_type:
            challenges=graded_assignment.challenges
        else:
            challenges=[graded_assignment]
        tools = [
        {
            "type": "function",
            "function": {
                "name": "enhance_feedback_with_interest",
                "description": "Enhance the feedback with information related to the student's interest.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "personalized_feedback": {
                            "type": "string",
                            "description": "edited personalized feedback for the student for that question. "
                        }
                    },
                    "required": ["personalized_feedback"]
                }
            }
        }
    ]

    except Exception as e:
        logger.error(f"Error in supporting_interest_agent before processing challenges: {e}")
        return
    try:
        with ThreadPoolExecutor() as executor:
            futures = {}
            for index, question in enumerate(challenges):
                logger.info(f"Processing challenge {index + 1}/{len(challenges)}")
                logger.info(f"Question: {question}")
                messages = langfuse.get_prompt("enhance_feedback_with_interest", label="latest")
                model = messages.config.get("openai", {}).get("model","gpt-4o")
                temperature = messages.config.get("openai", {}).get("temperature",0.5)
                max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
                logger.info(f'personalized_feedback: {getattr(question, "personalized_feedback", "")}')
                complete_chat_prompt = messages.compile(
                    question=getattr(question, 'task', ''),
                    students_answer=getattr(question, 'students_answer', ''),
                    answer_key=getattr(question, 'answerKey', ''),
                    interesting_information=interest_information,
                    current_personalized_feedback=getattr(question, 'personalized_feedback', ''),
                    grade_percentage=getattr(question, 'grade_percentage', ''),
                    grade=getattr(student_profile, 'grade', ''),
                    reading_level=getattr(student_profile, 'reading_level', '')
                )
                future  = executor.submit(make_openai_langfuse,complete_chat_prompt, tools, model, temperature, max_tokens)
                futures[future] = index
        for future in as_completed(futures):
            index = futures[future]
            try:
                response = future.result()
                logger.info(f"Response for challenge {index}: {response}")
                if hasattr(challenges[index], 'question_number'):
                    response['index']=challenges[index].question_number
                else:
                    response['index']=challenges[index].index
                challenges[index].personalized_feedback=response['personalized_feedback']
            except Exception as exc:
                logger.error(f"Failed to process challenge {index}: {exc}")
        logger.info(f"Final graded assignment: {graded_assignment}")
        return graded_assignment

    except Exception as e:
        logger.error(f"Error in supporting_interest_agent during ThreadPoolExecutor: {e}")
        return
   
def choose_random_interest_information(interest,grade):
    """
    Randomly chooses between 'real_time' or 'general' information.
    """
    main_category=random.choice(['real_time', 'general'])
    logger.info(f"Main Category: {main_category}")
    if main_category == 'real_time':
        sub_category = random.choice(['interest_search_news','interest_search_trends_and_events','interest_search_popular_discussions'])
        logger.info(f"Generating information for {sub_category}")
        messages = langfuse.get_prompt(sub_category, type='chat', label="latest")
        complete_chat_prompt = messages.compile(interest=interest,grade=grade)
        temperature = messages.config.get("openai", {}).get("temperature",0.5)
        model = messages.config.get("openai", {}).get("model","gpt-4o")
        max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "generate_search_queries",
                    "description": "Generate a list of search queries related to a specific topic or interest.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "search_queries": {
                                "type": "array",
                                "items": {
                                    "type": "string",
                                    "description": "A relevant search query related to the topic or interest provided."
                                },
                                "description": "List of search query strings."
                            }
                        },
                        "required": ["search_queries"]
                    }
                }
            }
        ]
        result = make_openai_langfuse(complete_chat_prompt, temperature=temperature, model=model, max_tokens=max_tokens,tools=tools)
        logger.info(f"Search Queries: {result}")
        interest_information=interest_search_agent(interest,grade,result['search_queries'])
        logger.info(f"Interest Information: {interest_information}")
        return str(interest_information)
    else:
        sub_category = random.choice(['interest_fun_facts_agent','interest_historical_facts','interest_famous_personalities','interest_related_topics'])
        logger.info(f"Generating information for {sub_category}")
        messages = langfuse.get_prompt(sub_category, type='chat', label="latest")
        complete_chat_prompt = messages.compile(interest=interest,grade=grade)
        temperature = messages.config.get("openai", {}).get("temperature",0.5)
        model = messages.config.get("openai", {}).get("model","gpt-4o")
        max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "generate_agent_information",
                    "description": "Generates a structured list of information based on the agent's predefined category of information focus.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "information": {
                                "type": "array",
                                "description": "Generated list of information entries tailored specifically to agent's category",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "name": {
                                            "type": "string",
                                            "description": "Name/title of the generated information"
                                        },
                                        "description": {
                                            "type": "string",
                                            "description": "Detailed description of the generated information"
                                        }
                                    },
                                    "required": ["name", "description"]
                                }
                            }
                        },
                        "required": ["information"]
                    }
                }
            }
        ]
        logger.info(f"complete chat prompt interestt: {complete_chat_prompt}" )
        result = make_openai_langfuse(complete_chat_prompt, temperature=temperature, model=model, max_tokens=max_tokens,tools=tools)
        logger.info(f"General Interest Information: {result}")
        return str(result['information'])
