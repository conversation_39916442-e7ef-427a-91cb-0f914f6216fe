from langgraph.graph import StateGraph,END
from service.grade_data_models import GradePipelineContext,to_native
from service.context_pipeline import logger
from service.grading.nodes.rubrics.new_york_next_generation_math_standards.final_answer_node import final_answer_node,final_answer_review_node
from service.grading.nodes.rubrics.new_york_next_generation_math_standards.method_node import method_node,method_review_node
from service.grading.nodes.rubrics.new_york_next_generation_math_standards.calculate_grade import calculate_grade_node
from service.grading.nodes.common_agents.math_expression_node import improve_feedback_with_math_expressions_node
from service.grading.nodes.common_agents.math_reasoning_node import improve_mathematical_reasoning_node
from service.grading.nodes.common_agents.verify_feedback_node import verify_feedback_node
def math_general_graph(ptx:GradePipelineContext,langfuse_handler):
    logger.info("Creating Passage Based Assignment Graph (Example Pattern Version)")
    ptx.tools=[
                {
                "type": "function",
                "function": {
                    "name": "grade_assignment",
                    "description": "Grade the given assignment ",
                    "parameters": {
                        "type": "object",
                        'properties': {
                        "graded_response": {
                            "type": "object",
                            "properties": {
                                "grade": {
                                    "type": "string",
                                    "description": "grade_earned_by_student for that question"
                                },
                                "personalized_feedback": {
                                    "type": "string",
                                    "description": "personalized feedback for the student for that question. Always have to be provided. "
                                },
                            },
                            "required":["grade","personalized_feedback"]
                            },
                        },
                        "required":["graded_response"]    
                        }
                    }
                }
            ]
    try:
        workflow = StateGraph(GradePipelineContext)
        workflow.add_node('final_answer', final_answer_node)
        workflow.add_node('method', method_node)
        workflow.add_node('final_answer_review', final_answer_review_node)
        workflow.add_node('method_review', method_review_node)
        workflow.add_node('calculate_grade', calculate_grade_node)
        workflow.add_node('improve_feedback_with_math_expressions', improve_feedback_with_math_expressions_node)
        workflow.add_node('improve_mathematical_reasoning', improve_mathematical_reasoning_node)
        workflow.add_node('verify_feedback', verify_feedback_node)
        workflow.set_entry_point('final_answer')
        workflow.add_edge('final_answer', 'method')
        workflow.add_edge('method', 'final_answer_review')
        workflow.add_edge('final_answer_review', 'method_review')
        workflow.add_edge('method_review', 'calculate_grade')
        workflow.add_edge('calculate_grade', 'improve_mathematical_reasoning')
        workflow.add_edge('improve_mathematical_reasoning', 'improve_feedback_with_math_expressions')
        workflow.add_edge('improve_feedback_with_math_expressions', 'verify_feedback')
        workflow.add_edge('verify_feedback', END)

        chain = workflow.compile()
    except Exception as e:
        logger.error(f"Error creating graph: {e}", exc_info=True)
        raise

    try:
        logger.info("Invoking graph...")
        result = chain.invoke(input=ptx, config={ "callbacks": [langfuse_handler] })
        logger.info(f"Graph invoked successfully: {result}")
        assignment=result['assignment']
        if hasattr(assignment, 'challenges'):
            assignment=assignment.challenges
        logger.info(f"Number of challenges to process: {len(assignment)}")
        for question in assignment:
            logger.info(f"Question before removing image_base64: {question}")
            if hasattr(question, 'image_base64'):
                del question.image_base64
                logger.info(f"Removed image_base64 from question: {question.index}")
        overall_grade=result['overall_grade']
        overall_personalized_feedback=result['overall_personalized_feedback']
        try:
            assignment = [a.model_dump() for a in assignment]
            logger.info("Successfully converted assignment to native format")
        except:
            logger.info("initial method failed trying via function")
            try:
                assignment=to_native(assignment)
            except:
                logger.info("to_native failed trying via literal eval")
        return {
            "challenges": assignment,
            "overall_grade": overall_grade,
            "overall_personalized_feedback": overall_personalized_feedback
        }
    except Exception as e:
        logger.error(f"Error invoking graph: {e}", exc_info=True)
        raise


