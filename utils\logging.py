import requests
import functools
from google.cloud import storage
import time
import json
from pytz import timezone
from datetime import datetime
function_logs = []

env = "int"

def log_function_io(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        func_name = func.__name__

        input_data = {"args": args, "kwargs": kwargs}
        result = func(*args, **kwargs)

        function_logs.append({
            "Function_name": func_name,
            "Input": input_data,
            "Output": result
        })

        return result
    return wrapper

def slack_logger(data):
    """Send a message to a Slack channel.
        The message needs to have a formatted structure, as the following
          student name, 
          assigment ID, 
          time of request (in NYC timezone), 
          status, 
          a url to request body as txt file, 
          url to response as txt file 
    """
    SLACK_WEBHOOK_URL = "*********************************************************************************"

    student_name = data.get("student_name")
    assignment_name = data.get("assignment_name")
    assignment_id = data.get("assignment_id") #check
    request_time = datetime.now(timezone('America/New_York')).strftime('%Y-%m-%d %H:%M:%S')
    status = data.get("status")
    request_body_url = data.get("request_body_url")
    response_url = data.get("response_url")
    image_url = data.get("image_url")
    error_message = data.get("error_message")
    time_taken = data.get("time_taken")
    request_id=data.get('request_id','')
    prod_errors=data.get("prod_errors", False)
    
    if prod_errors:
        SLACK_WEBHOOK_URL="*********************************************************************************"
    
    
    message = f"Student Name: {student_name}\n Assignment name:{assignment_name}\n Endpoint: Grading\nAssignment ID: {assignment_id}\nRequest Time: {request_time}\nStatus: {status}\nEnvironment: {env}\nError Message: {error_message}\n\nRequest Body: {request_body_url}\nResponse: {response_url} \nImage: {image_url}\nTime Taken: {time_taken}\n Request ID:{request_id} "
    slack_data = {'text': message}
    try:
        response = requests.post(
            SLACK_WEBHOOK_URL, data=json.dumps(slack_data),
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code != 200:
            raise ValueError(
                f'Request to slack returned an error {response.status_code}, the response is:\n{response.text}'
            )
    except Exception as e:
        print(f"Error sending message to Slack: {e}")

def upload_text_file_to_bucket(filename, text_data, session_id,protected=False):
    # Initialize the Google Cloud client
    client = storage.Client()
    bucket = client.bucket('uniqlearn-logging') 
    # make sure session_id is less than 1024 character limit
    session_id = session_id[:100]
    # Create a new blob and upload the file's content
    if protected:
        blob = bucket.blob(f'{env}/protected/grading/{session_id}/{filename}_{session_id}_{int(time.time())}.txt')
    else:    
        blob = bucket.blob(f'{env}/grading/{session_id}/{filename}_{session_id}_{int(time.time())}.txt')
    blob.upload_from_string(text_data, content_type='text/plain')

    # Return the public URL for the blob
    return blob.public_url

import base64
def upload_image_to_bucket(filename, base64_image, session_id):
    # Initialize the Google Cloud client
    client = storage.Client()
    bucket = client.bucket('uniqlearn-logging') 
    # Create a new blob and upload the file's content
    print(filename)
    if '.' in filename:
        extension = filename.split('.')[-1]
    else:
        extension = 'png'  # default to png if no extension is found
    # make sure session_id is less than 1024 character limit
    session_id = session_id[:100]
    # converting base64 to image
    image_data = base64.b64decode(base64_image)
    content_type = f'image/{extension}'
    blob = bucket.blob(f'{env}/grading/{session_id}/image_{session_id}_{int(time.time())}.{extension}')
    blob.upload_from_string(image_data, content_type=content_type)

    # Return the public URL for the blob
    return blob.public_url
