from service.grade_data_models import GradePipelineContext
from service.grading.common_agents.math_reasoning import improve_mathematical_reasoning
from service.context_pipeline import logger

def improve_mathematical_reasoning_node(state: GradePipelineContext):
    """
    Node that handles assessment of character strengths in assignments.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting improve_mathematical_reasoning_node")

    assignment = state.assignment
    student_profile = state.student
    tools=state.tools
    old_assignment = state.previous_response
    try:
        updated_assignment = improve_mathematical_reasoning(assignment, student_profile,tools,old_assignment)
        return {"assignment": updated_assignment}

    except Exception as e:
        logger.error(f"Error in improve_mathematical_reasoning_node: {e}")
        # Return no adjustments explicitly in case of error
        return {}
