from service.utils.supporting_functions import reading_level_example
from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
from concurrent.futures import ThreadPoolExecutor, as_completed

def improve_mathematical_reasoning(assignment, student_profile,tools,old_assignment=None):
    """
    This function enhances personalized feedback for math assignments by ensuring the quality of the mathematical reasoning of the feedback provided.

    Parameters:
    - assignment (dict): The JSON object containing the assignment details, including questions, student's answers, 
                            answer key, grade, and personalized feedback.
    - reading_level (str): The reading level of the student to ensure appropriate complexity in the feedback.
    - images (list): List of images representing the student's solution steps.
    - use_vision (bool): Whether to analyze the images using vision capabilities.
    - image_description (list): Detailed descriptions of the images provided.

    Returns:
    - dict: The response from the OpenAI API with the improved personalized feedback.
    """
    logger.info(f"Starting to improve feedback with math expressions for assignment.")
    logger.debug(f"Assignment object: {assignment}")
    logger.debug(f"Student profile: {student_profile}")
    logger.info(f"Old assignment provided: {old_assignment is not None}")

    try:
        reading_level = student_profile.reading_level
        example_text = reading_level_example(reading_level)
        logger.info(f"Student reading level: {reading_level}, Example text generated.")
    except Exception as e:
        logger.error(f"Error getting reading level or example text: {e}", exc_info=True)
        raise

    try:
        if hasattr(assignment, 'challenges'):
            challenges = assignment.challenges
        else:
            # If no 'challenges', treat the assignment as a single challenge item in a list
            challenges = assignment
            logger.error("Assignment object does not have a 'challenges' attribute, which is expected for math expression improvement.")
        
        logger.info(f"Processing {len(challenges)} challenges to improve feedback with math expressions.")
    except Exception as e:
        logger.error(f"Error extracting challenges from assignment: {e}", exc_info=True)
        raise

    with ThreadPoolExecutor() as executor:
        future_to_index = {}

        for index, question in enumerate(challenges):
            q_num_log = getattr(question, 'question_number', f"item_at_index_{index}")
            logger.info(f"Preparing task to improve feedback for challenge {index+1}/{len(challenges)} (ID/Num: {q_num_log})")

            try:
                if not hasattr(question, 'personalized_feedback') or not question.personalized_feedback:
                    logger.warning(f"Challenge (ID/Num: {q_num_log}) has no existing personalized feedback to improve. Skipping.")
                    continue

                previous_assignment_context = ""
                if old_assignment:
                    old_challenges=old_assignment['challenges']
                    logger.info(f"Old assignment found: {old_challenges}")
                    old_question = next((item for item in old_challenges if str(item.get('question_number')) == str(question.question_number)), {})
                    logger.info(f"Old question data for question {question.question_number}: {old_question}")
                    grade_percentage=old_question.get('grade_percentage', "")
                    students_answer=old_question.get('students_answer', "")
                    personalized_feedback=old_question.get('personalized_feedback', "")
                    rubrics_responses=old_question.get('rubrics_responses', "")
                    messages = langfuse.get_prompt("previous_grading_response_template", label="latest",)
                    logger.info(f"Retrieved prompt for previous grading response: {messages}")
                    previous_assignment_instructions = messages.compile(
                        grade_percentage=grade_percentage,
                        students_answer=students_answer,
                        personalized_feedback=personalized_feedback,
                        rubrics_responses=rubrics_responses
                    )
                    logger.info(f"Previous grading response prompt compiled: {previous_assignment_instructions}")

                langfuse_prompt_template = langfuse.get_prompt("improve_mathematical_reasoning", label="latest")
                
                current_prompt_data = {
                    "question_text": getattr(question, 'task', ''),
                    "students_answer": getattr(question, 'students_answer', ''),
                    "answer_key": getattr(question, 'answerKey', ''),
                    "current_feedback": question.personalized_feedback
                }
                complete_chat_prompt = langfuse_prompt_template.compile(
                    assignment=current_prompt_data,
                    example_text=example_text,
                    reading_level=reading_level,
                    previous_assignment_context=previous_assignment_context # Add new context
                )
                logger.debug(f"Compiled prompt for challenge (ID/Num: {q_num_log}): {str(complete_chat_prompt)[:500]}...")

                # Replicating image preparation from method.py
                # This prepares `updated_messages` with image data if available,
                # but `complete_chat_prompt` (without images) is passed to the LLM by default,
                # mirroring method.py's current behavior.
                updated_messages = complete_chat_prompt # Start with a copy
                if hasattr(question, 'image_base64') and question.image_base64:
                    try:
                        temp_updated_messages = []
                        for msg in complete_chat_prompt:
                            if msg.get("role") == "user":
                                new_content = [
                                    {"type": "text", "text": msg["content"]},
                                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{question.image_base64}", "detail": "high"}}
                                ]
                                temp_updated_messages.append({"role": "user", "content": new_content})
                            else:
                                temp_updated_messages.append(msg)
                        updated_messages = temp_updated_messages # Assign if successful
                        logger.info(f"Prepared message with image for question (ID/Num: {q_num_log}).")
                    except Exception as e_img:
                        logger.error(f"Error preparing image content for question (ID/Num: {q_num_log}): {e_img}", exc_info=True)
                        # Fallback to complete_chat_prompt if image processing fails

                future = executor.submit(make_openai_langfuse, updated_messages, tools, temperature=0.5)
                future_to_index[future] = index
                logger.info(f"Submitted task for challenge (ID/Num: {q_num_log}, index {index}) to executor.")

            except Exception as e:
                logger.error(f"Error preparing task for challenge (ID/Num: {q_num_log}, index {index}): {e}", exc_info=True)

        processed_challenges_count = 0
        for future in as_completed(future_to_index):
            original_idx = future_to_index[future]
            question_being_processed = challenges[original_idx]
            q_num_log = getattr(question_being_processed, 'question_number', f"item_at_index_{original_idx}")

            try:
                response_data = future.result() # Expected to be a dict with 'personalized_feedback'
                logger.info(f"Received LLM response for improving feedback for challenge (ID/Num: {q_num_log}): {response_data}")

                if isinstance(response_data, dict) and 'personalized_feedback' in response_data:
                    challenges[original_idx].personalized_feedback = response_data['personalized_feedback']
                    logger.info(f"Successfully updated personalized_feedback for challenge (ID/Num: {q_num_log}).")
                else:
                    logger.warning(f"LLM response for challenge (ID/Num: {q_num_log}) was not a dict or 'personalized_feedback' key missing. Response: {response_data}")
            except Exception as e:
                logger.error(f"Error processing LLM result for challenge (ID/Num: {q_num_log}): {e}", exc_info=True)
            processed_challenges_count += 1

    logger.info(f"Finished processing all {processed_challenges_count} submitted LLM tasks for improving feedback.")

    try:
        if hasattr(assignment, 'challenges'):
            assignment.challenges = challenges
        else:
            logger.error("Assignment object does not have a 'challenges' attribute, which is expected for math expression improvement.")
        
        logger.info("Personalized feedback enhanced with math expressions and assignment updated.")
        logger.debug(f"Final assignment object after improving feedback: {assignment}")
        return assignment
    except Exception as e:
        logger.error(f"Error finalizing assignment after improving feedback: {e}", exc_info=True)
        raise