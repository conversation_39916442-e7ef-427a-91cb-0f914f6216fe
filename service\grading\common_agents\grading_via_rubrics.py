from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import logger
from service.context_pipeline import langfuse
from service.utils.supporting_functions import reading_level_example,extract_question_text,generate_question_json,calculate_grade_from_questions
import copy
from concurrent.futures import ThreadPoolExecutor, as_completed

def grade_via_rubrics(assignment: dict,student_profile:dict, context:str, assignment_type:str,dyslexia:bool,old_assignment:dict) -> dict:
    """
    """
    logger.info(f"Starting grade_via_rubrics with assignment: {assignment}")
    logger.info(f"Student profile: {student_profile}")
    logger.info(f"Context: {context}")
    logger.info(f"Assignment type: {assignment_type}")
    logger.info(f"Dyslexia flag: {dyslexia}")
    logger.info(f"Old assignment provided: {old_assignment is not None}")

    try:
        if hasattr(assignment, 'challenges') and 'essay' not in assignment_type:
            challenges=assignment.challenges
        else:
            challenges=[assignment]
        logger.info(f"Number of challenges to process: {len(challenges)}")

        with ThreadPoolExecutor() as executor:
            futures = {}
            overall_grade = 0
            for index, question in enumerate(challenges):
                logger.info(f"Processing challenge {index + 1}/{len(challenges)}")

                rubrics = question.rubrics.copy()
                logger.info(f"Question {index} rubrics: {rubrics}")

                rubrics_questions=extract_question_text(rubrics)
                logger.info(f"Extracted rubrics questions for question {index}: {rubrics_questions}")

                logger.debug(f"Parsing assignment: {question}")

                previous_assignment_instructions = ""
                if old_assignment:
                    old_challenges=old_assignment['challenges']
                    logger.info(f"Old assignment found: {old_challenges}")
                    old_question = next((item for item in old_challenges if str(item.get('question_number')) == str(question.question_number)), {})
                    logger.info(f"Old question data for question {index}: {old_question}")
                    grade_percentage=old_question.get('grade_percentage', "")
                    students_answer=old_question.get('students_answer', "")
                    personalized_feedback=old_question.get('personalized_feedback', "")
                    rubrics_responses=old_question.get('rubrics_responses', "")
                    messages = langfuse.get_prompt("previous_grading_response_template", label="latest",)
                    logger.info(f"Retrieved prompt for previous grading response: {messages}")
                    previous_assignment_instructions = messages.compile(
                        grade_percentage=grade_percentage,
                        students_answer=students_answer,
                        personalized_feedback=personalized_feedback,
                        rubrics_responses=rubrics_responses
                    )
                    logger.info(f"Previous grading response prompt compiled: {previous_assignment_instructions}")

                if context:
                    context= f"This is the passage for the assignment: {context}"
                    logger.info(f"Context formatted: {context}")

                dyslexia_instructions = ""
                if dyslexia:
                    dyslexia_instructions = "\n\nIMPORTANT: The student has dyslexia. While grading, you MUST NOT penalize for spelling errors at all. Do not consider spelling mistakes when assigning a grade or providing feedback."
                    logger.info(f"Dyslexia instructions added: {dyslexia_instructions}")
                reading_example=reading_level_example(student_profile.reading_level)
                logger.info(f"Reading level example for question {index}: {reading_example}")

                logger.debug("Creating system and user messages for grading via rubrics")
                messages = langfuse.get_prompt("grade_via_rubrics_template", label="latest")
                logger.info(f"Retrieved grading template prompt for question {index}")

                complete_chat_prompt = messages.compile(
                    assignment=question,
                    student=student_profile,
                    context_based_instructions=context,
                    reading_example=reading_example,
                    dyslexia_instructions=dyslexia_instructions,
                    old_assignment_instructions=previous_assignment_instructions
                )
                logger.info(f"Compiled chat prompt for question {index}: {complete_chat_prompt}")

                tools = generate_question_json(rubrics_questions)
                logger.info(f"Generated tools for question {index}: {tools}")

                future  = executor.submit(make_openai_langfuse,complete_chat_prompt, tools)
                logger.info(f"Created future object for question {index}: {future}")
                futures[future] = index
                logger.info(f"Submitted grading request for question {index}, future object: {future}")

            logger.info("Processing completed futures from grading requests")
            for future in as_completed(futures):
                index = futures[future]
                logger.info(f"Processing completed future for question {index}")
                try:
                    response = future.result()
                    logger.info(f"Response for question {index}: {response}")
                    print("response:", response)

                    grade = calculate_grade_from_questions(response,5)
                    overall_grade += grade
                    logger.info(f"Calculated raw grade for question {index}: {grade}")
                    print("grade:", grade)

                    grade = round(float(grade), 2)
                    logger.info(f"Rounded grade for question {index}: {grade}")

                    temp=response.copy()
                    temp.pop('personalized_feedback','')
                    temp.pop('index','')
                    temp.pop('grade','')
                    logger.info(f"Processed response data for question {index}: {temp}")
                    logger.info(f"Updating challenges: {challenges}")
                    challenges[index].rubrics_responses=temp
                    challenges[index].grade=grade
                    challenges[index].personalized_feedback = response['personalized_feedback']
                    grade_percentage = (grade/5)*100
                    challenges[index].grade_percentage=grade_percentage
                    logger.info(f"Updated challenge {index} with grade percentage: {grade_percentage}%")

                except Exception as e:
                    logger.error(f"Error processing non-essay grading on question {index}: {e}")
                    print(f"Error processing non-essay grading on question {index}: {e}")
        overall_grade=overall_grade/len(challenges)
        if hasattr(assignment, 'challenges') and 'essay' not in assignment_type:
            assignment.challenges=challenges
        logger.info(f"Final assignment with updated challenges: {assignment.dict()}")
        logger.info("Successfully completed grade_via_rubrics processing")
        return assignment,overall_grade
    except Exception as e:
        logger.error(f"Error in grade_via_rubrics: {e}")
        raise


# assignment={
#   "challenges": [
#     {
#       "answerKeys": "Alex feels free and happy when swimming, like flying.",
#       "description": "Think about what 'flying underwater' tells us about how Alex feels when he swims.",
#       "index": "1",
#       "scenario": "John finds a mysterious map in his attic, pointing to the lost treasure of Numeria. The map is filled with mathematical puzzles that need solving to reveal the path.",
#       "students_answer": "Alex feels free and happy when swimming, like flying.",
#       "task": "What does 'flying underwater' mean about how Alex feels when he swims?",
#       "title": "The Mysterious Map"
#     }
#   ],
#   "state_standard": "California Common Core",
#   "topic": "5-ESS1-1 Support an argument that the apparent brightness of the sun and stars is due to their relative distances from Earth.",
#   "grade": "5th",
#   "reading_level": "5th",
#   "assignment_type": "reading_comp_gen"
# }

# student_profile={
#         "grade": "5th",
#         "reading_level": "5th",
#         "first_name": "Alex",
#         "last_name": "Smith",
#         "interest": "swimming",
#         "working_style": "Expressive",
#         "strengths": "Honesty, Compassion, Creativity"
#     }

# context="This is the passage for the assignment: Alex loves to swim. He feels free and happy when he swims. It's like flying underwater."   
# assignment_type="reading_comp_gen"
# dyslexia=False
# old_assignment=None

# grade_via_rubrics(assignment,student_profile, context, assignment_type,dyslexia,old_assignment)