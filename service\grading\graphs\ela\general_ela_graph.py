# service/graphs/ela/aipassage_langgraph.py
from langgraph.graph import StateGraph,END
from service.grade_data_models import GradePipelineContext,to_native
from service.context_pipeline import logger
from service.grading.nodes.common_agents.grading_via_rubrics_node import grade_via_rubrics_node
from service.grading.nodes.common_agents.summarize_personalized_feedback_node import summarize_personalized_feedback_node
from service.grading.nodes.common_agents.answer_feedback_questions_node import answer_feedback_questions_node
from service.grading.nodes.common_agents.verify_feedback_node import verify_feedback_node
from service.grading.nodes.common_agents.interest_inclusion_node import interest_inclusion_node,interest_search_node
def general_ela_graph(ptx:GradePipelineContext,langfuse_handler):
    logger.info("Creating Passage Based Assignment Graph (Example Pattern Version)")
    try:
        workflow = StateGraph(GradePipelineContext)
        workflow.add_node("start",start)
        workflow.add_node('grade_via_rubrics', grade_via_rubrics_node)
        workflow.add_node('summarize_feedback', summarize_personalized_feedback_node)
        workflow.add_node('answer_feedback_questions', answer_feedback_questions_node)
        workflow.add_node('verify_feedback', verify_feedback_node)
        workflow.add_node('supporting_interest', interest_inclusion_node)
        workflow.add_node('interest_search', interest_search_node)
        # workflow.add_node('translate_response', translate_response)
        
        workflow.set_entry_point('start')
        workflow.add_edge('start', 'interest_search')
        workflow.add_edge('start', 'grade_via_rubrics')
        workflow.add_edge('grade_via_rubrics', 'summarize_feedback')
        workflow.add_edge('summarize_feedback', 'answer_feedback_questions')
        workflow.add_edge(['interest_search','answer_feedback_questions'], 'supporting_interest')
        workflow.add_edge('supporting_interest', END)
        #workflow.add_edge('verify_feedback', END)
        chain = workflow.compile()
    except Exception as e:
        logger.error(f"Error creating graph: {e}", exc_info=True)
        raise

    try:
        logger.info("Invoking graph...")
        result = chain.invoke(input=ptx, config={ "callbacks": [langfuse_handler] })
        # result = chain.invoke(ptx)
        logger.info(f"Graph invoked successfully: {result}")
        assignment=result['assignment']
        if hasattr(assignment, 'challenges'):
            assignment=assignment.challenges
        overall_grade=result['overall_grade']
        overall_personalized_feedback=result['overall_personalized_feedback']
        try:
            assignment = [a.model_dump() for a in assignment]
            logger.info("Successfully converted assignment to native format")
        except:
            logger.info("initial method failed trying via function")
            try:
                assignment=to_native(assignment)
            except:
                logger.info("to_native failed trying via literal eval")
        return {
            "challenges": assignment,
            "overall_grade": overall_grade,
            "overall_personalized_feedback": overall_personalized_feedback
        }

    except Exception as e:
        logger.error(f"Error invoking graph: {e}", exc_info=True) 

def start(state:GradePipelineContext):
    return state