#maybe add reading_level example
from service.openai_utils.gpt_call import make_openai_langfuse
import os
from service.context_pipeline import langfuse,logger
from service.utils.supporting_functions import reading_level_example
import ast
import concurrent.futures
from service.grading.common_agents.assess_reading_level_agent import kincaid_grade_level_test_english,extract_and_convert_grade

def get_guideline_by_level(level):
    """
    This function retrieves the guideline for a given educational level. The guidelines include a description, examples, 
    and keywords tailored to different grade ranges. The function returns the corresponding guideline based on the 
    specified level.

    Parameters:
    - level (str): The educational level for which the guideline is needed (e.g., '1', '2', '9').

    Returns:
    - dict or None: The guideline containing description, examples, and keywords if the level is found; otherwise, returns None.
    """

    guidelines = {
    ('','kindergarten') : {
        "description": "Introduce basic concepts with very simple sentences. Focus on building foundational skills like counting, identifying shapes, and recognizing letters.",
        "examples": [
            "<PERSON> is happy. How many apples does he have?",
            "<PERSON> sees a red ball. What shape is it?",
            "<PERSON> has 3 pencils. How many more does he need to make 5?",
            "<PERSON> likes the colour yellow"
        ],
        "keywords": ["basic", "simple", "everyday"]
    }, 
    ('1', '2'): {
        "description": "Use short, predictable sentences (8-10 words) with 1 main clause. Limit to 1-2 common nouns/verbs per sentence. Avoid complex adjectives. Maintain simple subject-verb-object structure.",
        "examples": [
            "The energetic puppy chases seven bouncing tennis balls around the grassy backyard.",
            "Emma carefully counts twelve shiny red apples in her grandmother's woven basket.", 
            "Morning sunlight filters through six classroom windows, creating patterns on the tiled floor."  
        ],
        "keywords": ["short sentences", "single clause"]
    },
    ('3', '4'): {
        "description": "Compound sentences (10-13 words) with 2 clauses using 'and/but'. Include 2-3 concrete nouns/action verbs. Use basic descriptive adjectives. Introduce simple prepositional phrases.",
        "examples": [
            "Archaeologists discovered ancient pottery fragments, and they carefully documented each intricate design.",  # 13 words
            "Meteorologists predict heavy rainfall, but farmers welcome it for their thirsty crops.",  # 13 words
            "Urban planners propose new bike lanes, while considering existing traffic flow patterns."  # 13 words
        ],
        "keywords": ["compound sentences", "basic adjectives"]
    },
    ('5', '6'): {
        "description": "Multi-clause sentences (13-16 words) with 2-3 clauses. Use 'because/while' conjunctions. Include 3-4 specific nouns/verbs with descriptive adjectives. Introduce technical terms with context.",
        "examples": [
            "While conducting pH experiments, students wear protective goggles because chemical reactions can be unpredictable.",  # 16 words
            "Cartographers update topographic maps whenever geological surveys reveal new elevation measurements.",  # 12 words → expanded
            "Although renewable energy costs decrease annually, implementation requires substantial infrastructure investments."  # 14 words
        ],
        "keywords": ["multi-clause", "contextual terms"]
    },
    ('7', '8'): {
        "description": "Complex structures (16-19 words) with 3-4 clauses. Use 'however/therefore' transitions. Include 4-5 abstract nouns/verbs with precise adjectives. Introduce metaphorical language and nominalizations.",
        "examples": [
            "Biometric authentication systems, while enhancing security, raise ethical concerns regarding personal privacy and data protection.",  # 17 words
            "Cryptocurrency fluctuations demonstrate market volatility, yet blockchain technology continues evolving financial systems.",  # 14 words → expanded
            "Neuroscientific research reveals neural plasticity mechanisms that enable cognitive adaptation through deliberate practice."  # 16 words
        ],
        "keywords": ["transitions", "abstract concepts"]
    },
    ('9', '10'): {
        "description": "Sophisticated sentences (20-22 words) with embedded clauses. Use correlative conjunctions. Include 5-6 technical nouns/verbs with nuanced adjectives. Employ passive voice and conditional structures.",
        "examples":  [
            "Epistemological frameworks in quantum physics challenge classical notions of causality through probabilistic interpretations of subatomic interactions.",  # 18 words → expanded
            "Postcolonial literary critiques deconstruct Eurocentric narratives while amplifying marginalized voices through intersectional analysis of power dynamics.",  # 20 words
            "Sustainable architecture integrates biomimetic principles that emulate ecological systems to optimize energy efficiency in urban structures."  # 20 words
        ],
        "keywords": ["embedded clauses", "conditional structures"]
    },
    ('11', '12'): {
        "description": "Dense academic prose (22+ words) with multiple embedded clauses. Use advanced conjunction combinations. Include 7-8 specialized nouns/verbs with domain-specific adjectives. High lexical density with nominalizations.",
        "examples": [
            "The ontological implications of quantum decoherence theory fundamentally destabilize classical Cartesian dualism through non-local entanglement phenomena observed in controlled laboratory conditions.",  # 24 words
            "Hermeneutic phenomenology in continental philosophy necessitates rigorous examination of lived experience structures while bracketing presuppositions through methodological epochē.",  # 22 words
            "Neuroplasticity research demonstrates cortical reorganization capabilities wherein repetitive cognitive stimuli induce dendritic arborization across associational neocortical regions."  # 20 words → expanded
        ],
        "keywords": ["lexical density", "specialized language"]
    }
}

    for key_range in guidelines:
        if level in key_range:
            return guidelines[key_range]
    return None


def reading_prompt_to_use(reading_level, kincaid_grade_level):
    """
    Determines whether to increase or reduce the text complexity of an assignment based on the student's reading level and the 
    Kincaid grade level. If the difference between the levels is significant, a prompt for adjusting the text complexity is returned.

    Parameters:
    - reading_level (int): The student's reading level.
    - kincaid_grade_level (int): The Kincaid grade level of the assignment text.

    Returns:
    - str: A prompt to either increase or reduce text complexity, or an empty string if no adjustment is needed.
    """
    
    increase_text_complexity = """
    To enhance the complexity of the text, consider the following adjustments:
    1. **Expand Vocabulary**: Incorporate more sophisticated and subject-specific vocabulary. Avoid simple words where a more complex synonym can add depth without confusing the context.
    2. **Lengthen Sentences**: Gradually increase the sentence length by using more compound and complex sentences. Incorporate conjunctions, relative clauses, and adverbial phrases to develop intricate sentence structures.
    3. **Introduce Advanced Concepts**: Include abstract and inferential elements to challenge comprehension and analytical skills. Utilize metaphors, analogies, and hypothetical scenarios that are relevant to the topic.
    4. **Encourage Analytical Thinking**: Pose questions or scenarios that require reflection, hypothesis, and discussion, extending beyond straightforward informational content.
    5. **Use Passive and Subjunctive Moods**: Employ sentences that utilize passive voice for variety and subjunctive moods to explore hypothetical or contrary-to-fact scenarios.
    """

    reduce_text_complexity = """
    To simplify the text, consider implementing these strategies:
    1. **Shorten Sentences**: Break long sentences into shorter, more digestible ones. Focus on clarity and brevity, avoiding unnecessary subordinate clauses and complex constructions.
    2. **Simplify Vocabulary**: Use simpler, more common words suitable for the student's age group. Replace complex words or jargon with straightforward alternatives.
    3. **Use Active Voice**: Shift from passive to active voice to make sentences clearer and more direct, which aids in understanding the actions within the text.
    4. **Structure with Headers and Bullets**: Organize content with clear headers and bullet points to break information into manageable parts. This structure aids students in following and understanding the material.
    5. **Limit Paragraph Length**: Maintain short paragraphs, ideally containing only a few sentences focused on a single idea. This helps in keeping the reader's attention and understanding.
    """
    level_difference = reading_level - kincaid_grade_level
    if abs(level_difference) <= 1:
        return ""
    elif level_difference > 1:
        return increase_text_complexity
    else:
        return reduce_text_complexity
    
#@log_function_io    
def adjust_feedback_reading_level(assignment,student_profile, assignment_type,old_assignment=None):
    logger.info(f"adjust_feedback_reading_level: {assignment}")
    logger.info(f'old_assignment in adjust_feedback_reading_level: {old_assignment}')
    try:
        if isinstance(assignment, str):
            assignment = ast.literal_eval(assignment)
        logger.info(f"Successfully processed graded_assignment type conversion")
    except Exception as e:
        logger.error(f"Error processing graded_assignment type conversion: {e}")
        raise
    
    try:
        example_text = reading_level_example(student_profile.reading_level)
        logger.info(f"Reading level example text: {example_text}")
    except Exception as e:
        logger.error(f"Error getting reading level example: {e}")
        raise
    try:
        if hasattr(assignment, 'challenges') and 'essay' not in assignment_type:
            graded_assignment=assignment.challenges
        else:
            graded_assignment=[assignment]
        logger.info(f"Number of challenges to process: {len(graded_assignment)}")
    except Exception as e:
        logger.error(f"Error processing challenges: {e}")
        raise

    logger.info(f"Starting ThreadPoolExecutor for {len(graded_assignment)} questions")
    try:
        with concurrent.futures.ThreadPoolExecutor() as executor:
            #feedback, extra_prompt, reading_level, name
            reading_level=getattr(student_profile, 'reading_level', None)
            name=getattr(student_profile, 'first_name', '')
            feedback=getattr(question, 'personalized_feedback', '')
            future_to_original_index = dict()
            for i, question in enumerate(graded_assignment):
                feedback=getattr(question, 'personalized_feedback', '')
                grade_level=int(kincaid_grade_level_test_english(feedback))
                #log_function_call([json_response],grade_level,"kincaid_grade_level_test",'')    
                if type(reading_level)==str:
                    reading_level_int = extract_and_convert_grade(reading_level)
                elif type(reading_level)==int:
                    reading_level_int=reading_level
                print("reading level adjusted:",reading_level_int)
                extra_prompt=reading_prompt_to_use(reading_level_int,grade_level)
                print("Before reading level:",grade_level)
                future = executor.submit(
                    adjust_feedback_language, feedback, extra_prompt, reading_level, name
                )
                future_to_original_index[future] = i
            logger.info(f"Successfully created {len(future_to_original_index)} futures for processing")

            for future in concurrent.futures.as_completed(future_to_original_index):
                original_idx = future_to_original_index[future]
                try:
                    # response_data is a dict like {'personalized_feedback': ..., 'index': ...}
                    # The 'index' in response_data is still generated by process_single_question
                    # for internal use (e.g., adjust_feedback_language), but we use original_idx for mapping here.
                    response_data = future.result()
                    logger.info(f"Response from future for original index {original_idx}: {response_data}")
                    
                    # Directly update the personalized_feedback of the corresponding question object
                    if graded_assignment[original_idx] is not :
                        graded_assignment[original_idx].personalized_feedback = response_data['personalized_feedback']
                    else:
                        logger.warning(f"Could not update feedback for original index {original_idx}. Response: {response_data} or question item is None.")

                except Exception as exc:
                    logger.error(f"Error processing question at original index {original_idx}: {exc}")
                    # Optionally, handle the error for graded_assignment[original_idx], e.g., by setting feedback to an error message or None
    except Exception as e:
        logger.error(f"Error in ThreadPoolExecutor processing: {e}")
        raise

    # Personalized feedback has been directly assigned to the question objects in the graded_assignment list.
    logger.info("Personalized feedback assigned to questions during parallel processing.")

    try:
        if 'essay' in assignment_type:
            # If it's an essay, graded_assignment was a list with one item.
            # That item has been updated, so now we extract it.
            assignment=graded_assignment[0]
        else:
            assignment.challenges=graded_assignment
        logger.info(f"PROCESSED ASSIGNMENT: {graded_assignment}")
        return assignment
    except Exception as e:
        logger.error(f"Error finalizing processed assignment: {e}")
        raise
def adjust_feedback_language(feedback, extra_prompt, reading_level, name):
    """
    Adjusts the language of personalized feedback to match the specified reading level without altering the original message.

    Parameters:
    - feedback (str): The personalized feedback text to be adjusted.
    - extra_prompt (str): Instructions on whether to increase or decrease the complexity of the text.
    - reading_level (int): The target reading level for the student.

    Returns:
    - dict: The response from the OpenAI API with the adjusted feedback.
    """

    # Fetch guidelines for the specified reading level
    guidelines_to_use = get_guideline_by_level(str(reading_level))
    
    messages = langfuse.get_prompt("adjust_feedback_language", label="latest")
    complete_chat_prompt = messages.compile(
        name=name,
        reading_level=reading_level,
        guidelines_to_use= guidelines_to_use,
        extra_prompt= extra_prompt,
        feedback=feedback
    )

    tool= [
            {
                "type": "function",
                "function": {
                    "name": "adjust_reading_level",
                    "description": "adjust the reading level of the feedback",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "personalized_feedback": {
                                "type": "string",
                                "description": "edited personalized feedback adjusted to fit the students reading level"
                            },
                        },
                        "required": ['personalized_feedback']
                    }
                }
            }
        ]
    
    feedback=make_openai_langfuse(complete_chat_prompt,tool)
    return feedback['personalized_feedback']