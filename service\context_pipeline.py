from aisdk import Aisdk
import logging
from langfuse import Langfuse
import os

aisdk_object=Aisdk('grade','staging')
logger= logging.getLogger()

config_data=aisdk_object.config.config_data
logging.info(f"config data in gen {config_data}")
langfuse_public_key=config_data['LANGFUSE_PUBLIC_KEY']
langfuse_secret_key=config_data['LANGFUSE_SECRET_KEY']
os.environ["LANGFUSE_PUBLIC_KEY"] = langfuse_public_key
os.environ["LANGFUSE_SECRET_KEY"] = langfuse_secret_key

langfuse = Langfuse(
  secret_key=langfuse_secret_key,
  public_key=langfuse_public_key,
  host="https://us.cloud.langfuse.com"
)