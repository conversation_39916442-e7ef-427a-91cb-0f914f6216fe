from service.grade_data_models import GradePipelineContext
from service.context_pipeline import logger
from service.utils.supporting_functions import calculate_overall_grade
def calculate_grade_node(state: GradePipelineContext):
    logger.info("Starting calculate_grade_node")
    assignment = state.assignment
    if hasattr(assignment, 'challenges'):
        challenges=assignment.challenges
    else:
        challenges=[assignment]
    updated_challenges,overall_grade=calculate_overall_grade(challenges)
    logger.info(f"Updated challenges: {updated_challenges}")
    logger.info(f"Overall grade: {overall_grade}")
    assignment.challenges=updated_challenges
    #assignment.overall_grade=overall_grade
    tool = [
            {
                "type": "function",
                "function": {
                    "name": "adjust_feedback",
                    "description": "",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "personalized_feedback": {
                                "type": "string",
                                'description': "personalized feedback for the student after enhancing the mathematical reasoning." 
                            }
                        },
                        "required": ['personalized_feedback']
                    }
                }
            }
        ]
    return {"assignment": assignment,'tools':tool,'overall_grade':overall_grade}