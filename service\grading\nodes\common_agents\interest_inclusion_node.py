from service.grade_data_models import GradePipelineContext
from typing import Dict, Any
from service.grading.common_agents.interest_agent import supporting_interest_agent, choose_random_interest_information
from service.context_pipeline import logger

def interest_inclusion_node(state: GradePipelineContext) -> Dict[str, Any]:
    """
    Node that handles evaluating if an assignment effectively incorporates a student's interest.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting interest_inclusion_node")

    interest_information = state.interest_information
    assignment_type = state.request.assignment_type
    assignment=state.assignment
    student_profile=state.student
    try:
        result = supporting_interest_agent(
            graded_assignment=assignment,
            interest_information=interest_information,
            assignment_type=assignment_type,
            student_profile=student_profile
            )
        logger.debug(f"Supporting interest agent result: {result}")

        
        logger.info("Interest adjustment instructions updated.")

        return {"assignment": result}

    except Exception as e:
        logger.error(f"Error in interest_inclusion_node: {e}")
        return {}
    
def interest_search_node(state: GradePipelineContext) -> Dict[str, Any]:
    """
    Node that handles searching for interest information.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting interest_search_node")

    interest = state.student.interest
    grade = state.student.grade

    try:
        result = choose_random_interest_information(
            interest=interest,
            grade=grade
        )
        logger.info("Completed interest_search_node successfully")
        
    except Exception as e:
        logger.error(f"Error in interest_search_node: {e}")
        result = []

    return {"interest_information": result}
