from service.context_pipeline import logger,aisdk_object
from service.grade_data_models import GradeRequest,GradePipelineContext
from aisdk.data_models.people import Student
from aisdk.data_models import assignment_model
import random

def pre_process_grade_assignment(data,default_ctx,request_id):
    ASSIGNMENT_TYPE_MAP = {
                # Math Assignments
                'math_worked_example': assignment_model.MathWorkedExampleAssignment,
                'math_fact': assignment_model.MathFactAssignment,
                'math_home_dynamic': assignment_model.MathHomeDynamicAssignment,
                'math_partner': assignment_model.MathPartnerAssignment,
                'math_word_problem': assignment_model.MathWordProblemAssignment,
                'math_story': assignment_model.MathStoryAssignment,

                # ELA Passage
                'reading_comp_gen': assignment_model.ElaReadingAssignment,
                'vocab_fill': assignment_model.ElaVocabAssignment,

                # History Assignments
                'history_critical': assignment_model.HistoryCriticalAssignment,
                'history_fact': assignment_model.HistoryFactAssignment,
                'history_vocab': assignment_model.HistoryVocabAssignment,

                # Essay Assignments (adjust keys as needed)
                'history_essay': assignment_model.HistoryEssayAssignment,
                'inf_essay': assignment_model.ElaInfEssayAssignment,
                'arg_essay': assignment_model.ElaArgEssayAssignment,

            }
    """Pre-process grade assignment"""
    try:
        request_body=GradeRequest(**data)
        logger.info(f"Request body validated: {request_body}")
        #default_ctx.request=request_body
    except Exception as e:
        logger.error("Error retrieving student profile: %s", e)
    # if 'interests' in data:
    #     if isinstance(data['interests'], list):
    #         data['interest'] = random.choice(data['interests'])
    #     else:
    #         data['interest'] = data['interests']
    try:
        last_attempt=aisdk_object.firestore.get_latest_attempts_data(data['student_id'],data['assignment_type'],data['assignment_id'])
    except:
        last_attempt=None
        logger.info("No previous attempt found")
    #Fetching images from bucket using urls:
    if 'math' in data['assignment_type']:
        for question in data['answers']:
            if 'image' in question:
                question['image_base64']=aisdk_object.storage.get_image_as_base64(question['image'])
        logger.info("Images Fetched for math")
        
    default_ctx_dictionary = default_ctx.dict()
    default_ctx_dictionary['request_id']=request_id
    default_ctx_dictionary['student'] = Student(**data)
    default_ctx_dictionary['request']=request_body
    default_ctx_dictionary['previous_response']=last_attempt
    logger.info("adding students answer to assignment")
    assignment_cls = ASSIGNMENT_TYPE_MAP.get(data["assignment_type"])
    if 'challenges' in default_ctx_dictionary["assignment"]:
        challenges = default_ctx_dictionary["assignment"]["challenges"]
        answers=data['answers']
        idx_to_challenge = {str(ch["index"]): ch for ch in challenges}

        for ans in answers:
            qnum = str(ans.get("question_number"))
            if qnum not in idx_to_challenge:     
                continue
            challenge = idx_to_challenge[qnum]
            for key, value in ans.items():
                challenge[key] = value
            #logger.info(f"Challenge updated with answer: {challenge}")
    else:
        default_ctx_dictionary["assignment"]['students_answer']=data['answers'][0]['students_answer']
    default_ctx_dictionary["assignment"] = assignment_cls.parse_obj(default_ctx_dictionary["assignment"])

    try:
        ptx=GradePipelineContext(**default_ctx_dictionary)
        logger.info(f"GradePipelineContext created: {ptx}")
    except Exception as e:
        logger.error(f"Error creating GradePipelineContext: {e}")
    return ptx

# def pre_process_grade_assignment(data, default_ctx, request_id):
#     # ------------ assignment-type → model map -------------
#     ASSIGNMENT_TYPE_MAP = {
#         "math_worked_example": assignment_model.MathWorkedExampleAssignment,
#         "math_fact":            assignment_model.MathFactAssignment,
#         "math_home_dynamic":    assignment_model.MathHomeDynamicAssignment,
#         "math_partner":         assignment_model.MathPartnerAssignment,
#         "math_word_problem":    assignment_model.MathWordProblemAssignment,
#         "math_story":           assignment_model.MathStoryAssignment,
#         # ELA
#         "reading_comp_gen":     assignment_model.ElaReadingAssignment,
#         "vocab_fill":           assignment_model.ElaVocabAssignment,
#         # History
#         "history_critical":     assignment_model.HistoryCriticalAssignment,
#         "history_fact":         assignment_model.HistoryFactAssignment,
#         "history_vocab":        assignment_model.HistoryVocabAssignment,
#         # Essay
#         "history_essay":        assignment_model.HistoryEssayAssignment,
#         "inf_essay":            assignment_model.ElaInfEssayAssignment,
#         "arg_essay":            assignment_model.ElaArgEssayAssignment,
#     }

#     # ---------- validate inbound request -------------
#     request_body = GradeRequest(**data)

#     try:
#         last_attempt = aisdk_object.firestore.get_latest_attempts_data(
#             data["student_id"], data["assignment_type"], data["assignment_id"]
#         )
#     except Exception:
#         last_attempt = None

#     # ---------- flatten default_ctx -------------------
#     ctx_dict = default_ctx.dict()          # <-- loses model info, that's OK
#     ctx_dict.update(
#         request_id        = request_id,
#         student           = Student(**data),
#         request           = request_body,
#         previous_response = last_attempt,
#     )

#     # ----------  re-cast assignment -------------------
#     assignment_cls = ASSIGNMENT_TYPE_MAP.get(data["assignment_type"])
#     if assignment_cls is None:
#         raise ValueError(f"Unknown assignment_type: {data['assignment_type']}")

#     # turn the plain dict back into the right Pydantic subclass
#     ctx_dict["assignment"] = assignment_cls.parse_obj(ctx_dict["assignment"])

#     # ----------  merge student answers ---------------
#     answers = data["answers"]
#     assignment = ctx_dict["assignment"]

#     if hasattr(assignment, "challenges"):
#         idx = {str(ch.index): ch for ch in assignment.challenges}
#         for ans in answers:
#             if (ch := idx.get(str(ans["question_number"]))):
#                 ch.model_update(ans)
#     else:
#         assignment.students_answer = answers[0]["students_answer"]

#     # ----------  final GradePipelineContext -----------
#     ptx = GradePipelineContext(**ctx_dict)
#     return ptx
