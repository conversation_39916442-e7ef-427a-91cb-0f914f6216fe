from concurrent.futures import ThreadPoolExecutor, as_completed
from service.context_pipeline import logger
from service.context_pipeline import langfuse
from service.openai_utils.gpt_call import make_openai_langfuse

def verify_feedback(assignment, assignment_type, context="",old_assignment=None):
    """
    Sanitize / verify the feedback of every question in an assignment **in parallel**.
    ONLY this single public function is exposed – no extra helpers are defined.

    Parameters
    ----------
    assignment : any object or list / dict
        Something that either exposes `.challenges` (list-like) or represents a
        single question that must be verified.
    assignment_type : str
        E.g. "essay", "math", etc.  If it contains "essay" we treat the entire
        `assignment` as one item; otherwise we drill into `.challenges`.

    Returns
    -------
    The same `assignment`, but with each question’s feedback sanitized.
    """

    logger.info("verify_feedback ‑ start (type=%s)", assignment_type)

    # 1. Decide what we will iterate over
    if hasattr(assignment, "challenges") and "essay" not in assignment_type:
        questions = assignment.challenges
        update_original_object = True
    else:
        questions = [assignment]
        update_original_object = False

    logger.info("Number of items to sanitize: %d", len(questions))
    if context:
        additional_instructions= f"This is the passage for the assignment: {context}"
    with ThreadPoolExecutor() as executor:
        futures = {}

        for idx, question in enumerate(questions):
            cleaned_assignment = {
                "question_text": getattr(question, "task", ""),
                "students_answer": getattr(question, "students_answer", ""),
                "answer_key": getattr(question, "answerKey", ""),
                "current_feedback": getattr(question, "personalized_feedback", "")
            }
            if hasattr(question, "scenario"):
                cleaned_assignment["scenario"] = getattr(question, "scenario", "")

            prompt_template = langfuse.get_prompt("safeguard", label="latest")
            complete_chat_prompt = prompt_template.compile(assignment=cleaned_assignment,additional_instructions=additional_instructions)
            model=prompt_template.config.get("openai", {}).get("model","gpt-4.1-nano")
            temperature=prompt_template.config.get("openai", {}).get("temperature",0.5)
            max_tokens=prompt_template.config.get("openai", {}).get("max_tokens",4096)
            updated_messages = complete_chat_prompt
            if hasattr(question, 'image_base64') and question.image_base64:
                    try:
                        temp_updated_messages = []
                        for msg in complete_chat_prompt:
                            if msg.get("role") == "user":
                                new_content = [
                                    {"type": "text", "text": msg["content"]},
                                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{question.image_base64}", "detail": "high"}}
                                ]
                                temp_updated_messages.append({"role": "user", "content": new_content})
                            else:
                                temp_updated_messages.append(msg)
                        updated_messages = temp_updated_messages
                        logger.info(f"Prepared message with image for question (ID/Num: {question.question_number}).")
                    except Exception as e_img:
                        logger.error(f"Error preparing image content for question (ID/Num: {question.question_number}): {e_img}", exc_info=True)
            tools = [{
                "type": "function",
                "function": {
                    "name": "adjust_feedback",
                    "description": (
                        "Sanitize feedback by removing prohibited content while "
                        "preserving educational value"
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "personalized_feedback": {
                                "type": "string",
                                "description": (
                                    "Cleaned feedback without answers, solutions, "
                                    "pronouns, or offensive language"
                                )
                            }
                        },
                        "required": ["personalized_feedback"]
                    }
                }
            }]

            future = executor.submit(make_openai_langfuse,updated_messages,tools,model,temperature,max_tokens)
            futures[future] = idx
            logger.debug("Submitted future %s for question %d", future, idx)

        # 3. Collect results
        for future in as_completed(futures):
            idx = futures[future]
            try:
                response = future.result()
                logger.info(f"Response for question {idx}: {response}")
                questions[idx].personalized_feedback = response["personalized_feedback"]
                logger.info(f"Questions after processing: {questions[idx]}")
                logger.info("Sanitized question %d successfully", idx)
            except Exception as exc:
                logger.error("Sanitization failed for question %d: %s", idx, exc, exc_info=True)

    # 4. Stitch back into original structure
    if update_original_object:
        assignment.challenges = questions
    else:
        assignment = questions

    logger.info("verify_feedback ‑ finished")
    return assignment