from service.grade_data_models import GradePipelineContext
from service.context_pipeline import logger
from service.grading.rubrics.new_york_next_generation_math_standards.final_answer import final_answer_agent


def final_answer_node(state: GradePipelineContext):
    """
    Node that handles assessment of character strengths in assignments.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting final_answer_node")

    assignment = state.assignment
    student_profile = state.student
    assignment_type = state.request.assignment_type
    old_assignment = state.previous_response
    tools=state.tools
    logger.info(f"final_answer_node: {assignment}")
    try:
        updated_assignment = final_answer_agent(
            assignment, student_profile, assignment_type, old_assignment,tools
        )
        return {"assignment": updated_assignment}

    except Exception as e:
        logger.error(f"Error in final_answer_node: {e}")
        # Return no adjustments explicitly in case of error
        return {}
    
def final_answer_review_node(state: GradePipelineContext):
    """
    Node that handles assessment of character strengths in assignments.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting final_answer_node")

    assignment = state.assignment
    student_profile = state.student
    assignment_type = state.request.assignment_type
    old_assignment = state.previous_response
    tools=state.tools
    logger.info(f"final_answer_node: {assignment}")
    try:
        updated_assignment = final_answer_agent(
            assignment, student_profile, assignment_type, old_assignment,tools,review=True
        )
        return {"assignment": updated_assignment}

    except Exception as e:
        logger.error(f"Error in final_answer_node: {e}")
        # Return no adjustments explicitly in case of error
        return {}